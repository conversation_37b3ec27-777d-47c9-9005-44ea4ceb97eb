/**
 * Global setup for Jest tests
 * Ensures LocalStack is running and services are available
 */

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function checkLocalStackHealth() {
  try {
    const { stdout } = await execAsync('curl -s http://localhost:45660/_localstack/health');
    const health = JSON.parse(stdout);

    const requiredServices = ['lambda', 'apigateway', 'dynamodb', 's3', 'cognito-idp'];
    const availableServices = Object.keys(health.services);

    const allServicesRunning = requiredServices.every(service =>
      availableServices.includes(service) && health.services[service] === 'running'
    );

    if (allServicesRunning) {
      console.log('✅ LocalStack health check passed - all required services are running');
      return true;
    } else {
      console.log('❌ LocalStack health check failed - some services are not running');
      console.log('Required services:', requiredServices);
      console.log('Available services:', availableServices);
      return false;
    }
  } catch (error) {
    console.log('❌ LocalStack health check failed - unable to connect');
    console.log('Error:', error);
    return false;
  }
}

async function waitForLocalStack(maxAttempts = 30, delayMs = 2000) {
  console.log('🔄 Waiting for LocalStack to be ready...');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const isHealthy = await checkLocalStackHealth();

    if (isHealthy) {
      console.log(`✅ LocalStack is ready after ${attempt} attempts`);
      return;
    }

    if (attempt < maxAttempts) {
      console.log(`⏳ Attempt ${attempt}/${maxAttempts} failed, retrying in ${delayMs}ms...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  throw new Error(`❌ LocalStack failed to become ready after ${maxAttempts} attempts`);
}

module.exports = async function globalSetup() {
  console.log('🚀 Starting GameFlex AWS Backend Test Suite');
  console.log('📋 Environment:', process.env.NODE_ENV);
  console.log('🔗 LocalStack URL:', process.env.AWS_ENDPOINT_URL);

  try {
    await waitForLocalStack();
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    console.log('');
    console.log('💡 Make sure LocalStack is running:');
    console.log('   cd aws-backend && ./start.ps1');
    console.log('');
    throw error;
  }
}
