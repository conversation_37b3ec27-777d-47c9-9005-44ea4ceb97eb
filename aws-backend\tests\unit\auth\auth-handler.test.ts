/**
 * Unit tests for Auth Lambda handler
 * Tests authentication logic with mocked AWS services
 */

import { mockClient } from 'aws-sdk-client-mock';
import { 
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminInitiateAuthCommand,
  GetUserCommand,
  GlobalSignOutCommand
} from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBDocumentClient, PutCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';

// Import the handler
import { handler } from '../../../lambda-functions/auth/src/handler';
import { TestDataGenerator } from '../../utils/test-data';

// Mock AWS clients
const cognitoMock = mockClient(CognitoIdentityProviderClient);
const dynamoMock = mockClient(DynamoDBDocumentClient);

describe('Auth Lambda Handler', () => {
  beforeEach(() => {
    cognitoMock.reset();
    dynamoMock.reset();
    
    // Set up environment variables
    process.env.COGNITO_USER_POOL_ID = 'us-east-1_TestPool';
    process.env.COGNITO_USER_POOL_CLIENT_ID = 'test-client-id';
  });

  describe('Signup Handler', () => {
    it('should create a new user successfully', async () => {
      const testUser = TestDataGenerator.createUser();
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify({
          email: testUser.email,
          password: 'TestPassword123!',
          username: testUser.username
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      // Mock Cognito responses
      cognitoMock.on(AdminCreateUserCommand).resolves({
        User: { Username: testUser.cognito_user_id }
      });
      cognitoMock.on(AdminSetUserPasswordCommand).resolves({});

      // Mock DynamoDB responses
      dynamoMock.on(QueryCommand).resolves({ Items: [] }); // No existing user
      dynamoMock.on(PutCommand).resolves({});

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User created successfully');
      expect(body.user.email).toBe(testUser.email);
      expect(body.user.username).toBe(testUser.username);
      expect(body.user.id).toBeValidUUID();

      // Verify AWS service calls
      expect(cognitoMock).toHaveReceivedCommandWith(AdminCreateUserCommand, {
        UserPoolId: 'us-east-1_TestPool',
        Username: testUser.email,
        UserAttributes: [
          { Name: 'email', Value: testUser.email },
          { Name: 'email_verified', Value: 'true' }
        ]
      });

      expect(dynamoMock).toHaveReceivedCommandWith(PutCommand, {
        TableName: 'Users',
        Item: expect.objectContaining({
          email: testUser.email,
          username: testUser.username,
          cognito_user_id: testUser.cognito_user_id
        })
      });
    });

    it('should return error for missing required fields', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify({
          email: '<EMAIL>'
          // Missing password and username
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email, password, and username are required');
    });

    it('should handle existing user error', async () => {
      const testUser = TestDataGenerator.createUser();
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify({
          email: testUser.email,
          password: 'TestPassword123!',
          username: testUser.username
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      // Mock Cognito to throw UsernameExistsException
      cognitoMock.on(AdminCreateUserCommand).rejects({
        name: 'UsernameExistsException',
        message: 'User already exists'
      });

      const result = await handler(event, context);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User already exists');
    });

    it('should handle invalid JSON in request body', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: 'invalid json'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid JSON in request body');
    });
  });

  describe('Signin Handler', () => {
    it('should authenticate user successfully', async () => {
      const testUser = TestDataGenerator.createUser();
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify({
          email: testUser.email,
          password: 'TestPassword123!'
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      // Mock Cognito responses
      cognitoMock.on(AdminInitiateAuthCommand).resolves({
        AuthenticationResult: {
          AccessToken: 'test-access-token',
          RefreshToken: 'test-refresh-token',
          IdToken: 'test-id-token'
        }
      });
      cognitoMock.on(GetUserCommand).resolves({
        Username: testUser.cognito_user_id
      });

      // Mock DynamoDB responses
      dynamoMock.on(QueryCommand).resolves({
        Items: [testUser]
      });
      dynamoMock.on(UpdateCommand).resolves({});

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Authentication successful');
      expect(body.tokens.access_token).toBe('test-access-token');
      expect(body.tokens.refresh_token).toBe('test-refresh-token');
      expect(body.tokens.id_token).toBe('test-id-token');
      expect(body.user.id).toBe(testUser.id);
      expect(body.user.email).toBe(testUser.email);

      // Verify last login was updated
      expect(dynamoMock).toHaveReceivedCommandWith(UpdateCommand, {
        TableName: 'Users',
        Key: { id: testUser.id },
        UpdateExpression: 'SET last_login = :lastLogin'
      });
    });

    it('should return error for invalid credentials', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      // Mock Cognito to throw NotAuthorizedException
      cognitoMock.on(AdminInitiateAuthCommand).rejects({
        name: 'NotAuthorizedException',
        message: 'Incorrect username or password'
      });

      const result = await handler(event, context);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid email or password');
    });

    it('should return error for missing credentials', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify({
          email: '<EMAIL>'
          // Missing password
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email and password are required');
    });

    it('should return error when user profile not found', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPassword123!'
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      // Mock successful Cognito auth but no user in database
      cognitoMock.on(AdminInitiateAuthCommand).resolves({
        AuthenticationResult: {
          AccessToken: 'test-access-token',
          RefreshToken: 'test-refresh-token',
          IdToken: 'test-id-token'
        }
      });
      cognitoMock.on(GetUserCommand).resolves({
        Username: 'cognito-user-id'
      });
      dynamoMock.on(QueryCommand).resolves({ Items: [] }); // No user found

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User profile not found');
    });
  });

  describe('Refresh Token Handler', () => {
    it('should refresh token successfully', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify({
          refresh_token: 'test-refresh-token'
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      cognitoMock.on(AdminInitiateAuthCommand).resolves({
        AuthenticationResult: {
          AccessToken: 'new-access-token',
          IdToken: 'new-id-token'
        }
      });

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Token refreshed successfully');
      expect(body.tokens.access_token).toBe('new-access-token');
      expect(body.tokens.id_token).toBe('new-id-token');
    });

    it('should return error for invalid refresh token', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify({
          refresh_token: 'invalid-token'
        })
      });
      const context = TestDataGenerator.createLambdaContext();

      cognitoMock.on(AdminInitiateAuthCommand).rejects({
        name: 'NotAuthorizedException',
        message: 'Invalid refresh token'
      });

      const result = await handler(event, context);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid refresh token');
    });
  });

  describe('Signout Handler', () => {
    it('should sign out user successfully', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signout',
        headers: {
          Authorization: 'Bearer test-access-token'
        }
      });
      const context = TestDataGenerator.createLambdaContext();

      cognitoMock.on(GlobalSignOutCommand).resolves({});

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Signed out successfully');
    });

    it('should return error for missing authorization header', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signout',
        headers: {}
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Authorization header required');
    });
  });

  describe('CORS and Options', () => {
    it('should handle OPTIONS requests', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'OPTIONS',
        path: '/auth/signup'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Methods']).toContain('POST');
      expect(result.headers['Access-Control-Allow-Headers']).toContain('Authorization');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify({}) // Invalid body to trigger error
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
    });
  });

  describe('Unknown Endpoints', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/auth/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Endpoint not found');
    });
  });
});
