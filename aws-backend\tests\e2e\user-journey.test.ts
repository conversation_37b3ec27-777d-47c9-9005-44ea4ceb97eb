/**
 * End-to-end integration tests for complete user journeys
 * Tests the full flow from API Gateway through Lambda to DynamoDB and S3
 */

import { 
  AuthApiClient, 
  PostsApiClient, 
  MediaApiClient, 
  UsersApiClient,
  expectSuccessResponse,
  expectErrorResponse,
  createTestUser,
  authenticateTestUser
} from '../utils/api-helpers';
import { TestDataGenerator } from '../utils/test-data';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, S<PERSON><PERSON><PERSON><PERSON>, CognitoHelper } from '../utils/aws-helpers';

describe('End-to-End User Journey Tests', () => {
  let authClient: AuthApiClient;
  let postsClient: PostsApiClient;
  let mediaClient: MediaApiClient;
  let usersClient: UsersApiClient;
  
  let testUserPoolId: string;
  let testClientId: string;

  beforeAll(async () => {
    // Initialize API clients
    authClient = new AuthApiClient();
    postsClient = new PostsApiClient();
    mediaClient = new MediaApiClient();
    usersClient = new UsersApiClient();

    // Create test infrastructure
    testUserPoolId = await CognitoHelper.createUserPool('e2e-test-pool');
    testClientId = await CognitoHelper.createUserPoolClient(testUserPoolId, 'e2e-test-client');
    
    process.env.COGNITO_USER_POOL_ID = testUserPoolId;
    process.env.COGNITO_USER_POOL_CLIENT_ID = testClientId;

    // Create required tables
    await DynamoDBHelper.createTable(
      'Users',
      [{ AttributeName: 'id', KeyType: 'HASH' }],
      [{ AttributeName: 'id', AttributeType: 'S' }]
    );

    await DynamoDBHelper.createTable(
      'Posts',
      [{ AttributeName: 'id', KeyType: 'HASH' }],
      [{ AttributeName: 'id', AttributeType: 'S' }]
    );

    await DynamoDBHelper.createTable(
      'Media',
      [{ AttributeName: 'id', KeyType: 'HASH' }],
      [{ AttributeName: 'id', AttributeType: 'S' }]
    );

    // Create required S3 buckets
    await S3Helper.createBucket('gameflex-media-development');
    await S3Helper.createBucket('gameflex-avatars-development');
    await S3Helper.createBucket('gameflex-temp-development');
  });

  beforeEach(async () => {
    // Clear all test data
    await DynamoDBHelper.clearTable('Users');
    await DynamoDBHelper.clearTable('Posts');
    await DynamoDBHelper.clearTable('Media');
    await CognitoHelper.clearUsers(testUserPoolId);
    await S3Helper.clearBucket('gameflex-media-development');
    await S3Helper.clearBucket('gameflex-avatars-development');
    await S3Helper.clearBucket('gameflex-temp-development');
  });

  afterAll(async () => {
    // Cleanup infrastructure
    await CognitoHelper.deleteUserPool(testUserPoolId);
    await DynamoDBHelper.deleteTable('Users');
    await DynamoDBHelper.deleteTable('Posts');
    await DynamoDBHelper.deleteTable('Media');
    await S3Helper.deleteBucket('gameflex-media-development');
    await S3Helper.deleteBucket('gameflex-avatars-development');
    await S3Helper.deleteBucket('gameflex-temp-development');
  });

  describe('Complete User Registration and Authentication Flow', () => {
    it('should complete full user registration and authentication journey', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'JourneyPassword123!',
        username: 'journeyuser'
      };

      // Step 1: User registration
      const signupResponse = await authClient.signup(userData.email, userData.password, userData.username);
      expectSuccessResponse(signupResponse, 201);
      
      const userId = signupResponse.body.user.id;
      expect(userId).toBeValidUUID();
      expect(signupResponse.body.user.email).toBe(userData.email);
      expect(signupResponse.body.user.username).toBe(userData.username);

      // Step 2: User authentication
      const signinResponse = await authClient.signin(userData.email, userData.password);
      expectSuccessResponse(signinResponse, 200);
      
      const { access_token, refresh_token, id_token } = signinResponse.body.tokens;
      expect(access_token).toBeDefined();
      expect(refresh_token).toBeDefined();
      expect(id_token).toBeDefined();
      
      // Verify user data matches
      expect(signinResponse.body.user.id).toBe(userId);
      expect(signinResponse.body.user.email).toBe(userData.email);

      // Step 3: Token refresh
      const refreshResponse = await authClient.refreshToken(refresh_token);
      expectSuccessResponse(refreshResponse, 200);
      
      const newAccessToken = refreshResponse.body.tokens.access_token;
      expect(newAccessToken).toBeDefined();
      expect(newAccessToken).not.toBe(access_token); // Should be a new token

      // Step 4: Sign out
      const signoutResponse = await authClient.signout(newAccessToken);
      expectSuccessResponse(signoutResponse, 200);
      expect(signoutResponse.body.message).toBe('Signed out successfully');

      // Step 5: Verify token is invalidated (optional - depends on implementation)
      // const invalidTokenResponse = await authClient.signout(newAccessToken);
      // expect(invalidTokenResponse.statusCode).toBeGreaterThanOrEqual(400);
    });
  });

  describe('Content Creation and Management Flow', () => {
    let userAuth: any;

    beforeEach(async () => {
      // Create and authenticate a test user
      const testUser = await createTestUser();
      userAuth = await authenticateTestUser(testUser.email, testUser.password);
      
      // Set auth token for all clients
      postsClient.setAuthToken(userAuth.accessToken);
      mediaClient.setAuthToken(userAuth.accessToken);
      usersClient.setAuthToken(userAuth.accessToken);
    });

    it('should complete full content creation and management journey', async () => {
      // Step 1: Create a text post
      const postData = {
        title: 'My First Post',
        content: 'This is my first post on GameFlex!'
      };

      const createPostResponse = await postsClient.createPost(postData.title, postData.content);
      expectSuccessResponse(createPostResponse, 201);
      
      const postId = createPostResponse.body.post.id;
      expect(postId).toBeValidUUID();
      expect(createPostResponse.body.post.title).toBe(postData.title);
      expect(createPostResponse.body.post.content).toBe(postData.content);
      expect(createPostResponse.body.post.user_id).toBe(userAuth.user.id);

      // Step 2: Upload media
      const testImageBuffer = Buffer.from('fake-image-data');
      const uploadResponse = await mediaClient.uploadMedia(
        testImageBuffer,
        'test-image.jpg',
        'image/jpeg'
      );
      expectSuccessResponse(uploadResponse, 201);
      
      const mediaId = uploadResponse.body.media.id;
      const mediaUrl = uploadResponse.body.media.url;
      expect(mediaId).toBeValidUUID();
      expect(mediaUrl).toBeDefined();

      // Step 3: Create a post with media
      const mediaPostResponse = await postsClient.createPost(
        'Post with Image',
        'Check out this cool image!',
        mediaUrl
      );
      expectSuccessResponse(mediaPostResponse, 201);
      
      const mediaPostId = mediaPostResponse.body.post.id;
      expect(mediaPostResponse.body.post.media_url).toBe(mediaUrl);

      // Step 4: Retrieve all user posts
      const userPostsResponse = await usersClient.getUserPosts(userAuth.user.id);
      expectSuccessResponse(userPostsResponse, 200);
      
      expect(userPostsResponse.body.posts).toHaveLength(2);
      const postIds = userPostsResponse.body.posts.map((p: any) => p.id);
      expect(postIds).toContain(postId);
      expect(postIds).toContain(mediaPostId);

      // Step 5: Get individual post
      const getPostResponse = await postsClient.getPost(postId);
      expectSuccessResponse(getPostResponse, 200);
      expect(getPostResponse.body.post.id).toBe(postId);
      expect(getPostResponse.body.post.title).toBe(postData.title);

      // Step 6: Update post
      const updateData = { title: 'Updated Post Title' };
      const updateResponse = await postsClient.updatePost(postId, updateData);
      expectSuccessResponse(updateResponse, 200);
      expect(updateResponse.body.post.title).toBe(updateData.title);

      // Step 7: Delete post
      const deleteResponse = await postsClient.deletePost(postId);
      expectSuccessResponse(deleteResponse, 200);

      // Step 8: Verify post is deleted
      const getDeletedPostResponse = await postsClient.getPost(postId);
      expectErrorResponse(getDeletedPostResponse, 404);
    });
  });

  describe('User Profile Management Flow', () => {
    let userAuth: any;

    beforeEach(async () => {
      const testUser = await createTestUser();
      userAuth = await authenticateTestUser(testUser.email, testUser.password);
      usersClient.setAuthToken(userAuth.accessToken);
    });

    it('should complete full profile management journey', async () => {
      // Step 1: Get initial profile
      const initialProfileResponse = await usersClient.getProfile();
      expectSuccessResponse(initialProfileResponse, 200);
      
      const profile = initialProfileResponse.body.user;
      expect(profile.id).toBe(userAuth.user.id);
      expect(profile.email).toBe(userAuth.user.email);

      // Step 2: Update profile
      const updateData = {
        display_name: 'Updated Display Name',
        bio: 'This is my updated bio'
      };

      const updateResponse = await usersClient.updateProfile(updateData);
      expectSuccessResponse(updateResponse, 200);
      expect(updateResponse.body.user.display_name).toBe(updateData.display_name);

      // Step 3: Verify profile was updated
      const updatedProfileResponse = await usersClient.getProfile();
      expectSuccessResponse(updatedProfileResponse, 200);
      expect(updatedProfileResponse.body.user.display_name).toBe(updateData.display_name);

      // Step 4: Get profile by ID (public view)
      const publicProfileResponse = await usersClient.getProfile(userAuth.user.id);
      expectSuccessResponse(publicProfileResponse, 200);
      expect(publicProfileResponse.body.user.id).toBe(userAuth.user.id);
      expect(publicProfileResponse.body.user.display_name).toBe(updateData.display_name);
    });
  });

  describe('Multi-User Interaction Flow', () => {
    let user1Auth: any;
    let user2Auth: any;

    beforeEach(async () => {
      // Create two test users
      const testUser1 = await createTestUser('<EMAIL>', 'Password123!', 'user1');
      const testUser2 = await createTestUser('<EMAIL>', 'Password123!', 'user2');
      
      user1Auth = await authenticateTestUser(testUser1.email, testUser1.password);
      user2Auth = await authenticateTestUser(testUser2.email, testUser2.password);
    });

    it('should handle multi-user content interaction', async () => {
      // User 1 creates a post
      postsClient.setAuthToken(user1Auth.accessToken);
      const createPostResponse = await postsClient.createPost(
        'User 1 Post',
        'This is a post from user 1'
      );
      expectSuccessResponse(createPostResponse, 201);
      const postId = createPostResponse.body.post.id;

      // User 2 views the post
      postsClient.setAuthToken(user2Auth.accessToken);
      const viewPostResponse = await postsClient.getPost(postId);
      expectSuccessResponse(viewPostResponse, 200);
      expect(viewPostResponse.body.post.user_id).toBe(user1Auth.user.id);

      // User 2 views User 1's profile
      usersClient.setAuthToken(user2Auth.accessToken);
      const profileResponse = await usersClient.getProfile(user1Auth.user.id);
      expectSuccessResponse(profileResponse, 200);
      expect(profileResponse.body.user.id).toBe(user1Auth.user.id);

      // User 2 views User 1's posts
      const userPostsResponse = await usersClient.getUserPosts(user1Auth.user.id);
      expectSuccessResponse(userPostsResponse, 200);
      expect(userPostsResponse.body.posts).toHaveLength(1);
      expect(userPostsResponse.body.posts[0].id).toBe(postId);

      // User 2 cannot delete User 1's post (authorization test)
      const deleteResponse = await postsClient.deletePost(postId);
      expectErrorResponse(deleteResponse, 403); // Assuming authorization is implemented
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid authentication gracefully', async () => {
      // Try to access protected endpoints without authentication
      const response = await postsClient.createPost('Test Post', 'Test Content');
      expectErrorResponse(response, 401);
    });

    it('should handle non-existent resources gracefully', async () => {
      const testUser = await createTestUser();
      const userAuth = await authenticateTestUser(testUser.email, testUser.password);
      postsClient.setAuthToken(userAuth.accessToken);

      // Try to get non-existent post
      const response = await postsClient.getPost('non-existent-id');
      expectErrorResponse(response, 404);
    });

    it('should handle malformed requests gracefully', async () => {
      const testUser = await createTestUser();
      const userAuth = await authenticateTestUser(testUser.email, testUser.password);
      postsClient.setAuthToken(userAuth.accessToken);

      // Try to create post with invalid data
      const response = await postsClient.post('/posts', {
        // Missing required fields
        invalid_field: 'invalid_value'
      });
      expectErrorResponse(response, 400);
    });
  });
});
