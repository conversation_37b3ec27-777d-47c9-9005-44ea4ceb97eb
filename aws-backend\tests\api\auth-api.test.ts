/**
 * API Gateway integration tests for Auth endpoints
 * Tests the complete HTTP request/response flow through API Gateway to Lambda
 */

import { AuthApiClient, expectSuccessResponse, expectErrorResponse, expectCorsHeaders } from '../utils/api-helpers';
import { TestDataGenerator, TEST_CREDENTIALS } from '../utils/test-data';
import { DynamoDB<PERSON>elper, CognitoHelper } from '../utils/aws-helpers';

describe('Auth API Integration Tests', () => {
  let authClient: AuthApiClient;
  let testUserPoolId: string;
  let testClientId: string;

  beforeAll(async () => {
    authClient = new AuthApiClient();
    
    // Create test user pool and client
    testUserPoolId = await CognitoHelper.createUserPool('test-pool');
    testClientId = await CognitoHelper.createUserPoolClient(testUserPoolId, 'test-client');
    
    // Update environment variables
    process.env.COGNITO_USER_POOL_ID = testUserPoolId;
    process.env.COGNITO_USER_POOL_CLIENT_ID = testClientId;
    
    // Ensure Users table exists
    await DynamoDBHelper.createTable(
      'Users',
      [{ AttributeName: 'id', KeyType: 'HASH' }],
      [{ AttributeName: 'id', AttributeType: 'S' }]
    );
  });

  beforeEach(async () => {
    // Clear test data before each test
    await DynamoDBHelper.clearTable('Users');
    await CognitoHelper.clearUsers(testUserPoolId);
  });

  afterAll(async () => {
    // Cleanup
    await CognitoHelper.deleteUserPool(testUserPoolId);
    await DynamoDBHelper.deleteTable('Users');
  });

  describe('POST /auth/signup', () => {
    it('should create a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        username: 'newuser'
      };

      const response = await authClient.signup(userData.email, userData.password, userData.username);

      expectSuccessResponse(response, 201);
      expectCorsHeaders(response);

      expect(response.body.message).toBe('User created successfully');
      expect(response.body.user).toBeDefined();
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.username).toBe(userData.username);
      expect(response.body.user.id).toBeValidUUID();
    });

    it('should return error for missing email', async () => {
      const response = await authClient.post('/auth/signup', {
        password: 'TestPassword123!',
        username: 'testuser'
      });

      expectErrorResponse(response, 400, 'Email, password, and username are required');
      expectCorsHeaders(response);
    });

    it('should return error for missing password', async () => {
      const response = await authClient.post('/auth/signup', {
        email: '<EMAIL>',
        username: 'testuser'
      });

      expectErrorResponse(response, 400, 'Email, password, and username are required');
    });

    it('should return error for missing username', async () => {
      const response = await authClient.post('/auth/signup', {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

      expectErrorResponse(response, 400, 'Email, password, and username are required');
    });

    it('should return error for duplicate user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        username: 'duplicate'
      };

      // Create user first time
      const firstResponse = await authClient.signup(userData.email, userData.password, userData.username);
      expectSuccessResponse(firstResponse, 201);

      // Try to create same user again
      const secondResponse = await authClient.signup(userData.email, userData.password, userData.username);
      expectErrorResponse(secondResponse, 409, 'User already exists');
    });

    it('should return error for invalid email format', async () => {
      const response = await authClient.post('/auth/signup', {
        email: 'invalid-email',
        password: 'TestPassword123!',
        username: 'testuser'
      });

      // Note: This might be handled by Cognito validation
      expect(response.statusCode).toBeGreaterThanOrEqual(400);
    });

    it('should handle malformed JSON', async () => {
      const response = await authClient.post('/auth/signup', 'invalid json', {
        'Content-Type': 'application/json'
      });

      expectErrorResponse(response, 400, 'Invalid JSON');
    });
  });

  describe('POST /auth/signin', () => {
    beforeEach(async () => {
      // Create a test user for signin tests
      await authClient.signup(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password,
        'testuser'
      );
    });

    it('should authenticate user successfully', async () => {
      const response = await authClient.signin(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password
      );

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);

      expect(response.body.message).toBe('Authentication successful');
      expect(response.body.tokens).toBeDefined();
      expect(response.body.tokens.access_token).toBeDefined();
      expect(response.body.tokens.refresh_token).toBeDefined();
      expect(response.body.tokens.id_token).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.user.email).toBe(TEST_CREDENTIALS.VALID.email);
    });

    it('should return error for invalid email', async () => {
      const response = await authClient.signin(
        TEST_CREDENTIALS.INVALID_EMAIL.email,
        TEST_CREDENTIALS.INVALID_EMAIL.password
      );

      expectErrorResponse(response, 401, 'Invalid email or password');
    });

    it('should return error for invalid password', async () => {
      const response = await authClient.signin(
        TEST_CREDENTIALS.INVALID_PASSWORD.email,
        TEST_CREDENTIALS.INVALID_PASSWORD.password
      );

      expectErrorResponse(response, 401, 'Invalid email or password');
    });

    it('should return error for missing email', async () => {
      const response = await authClient.post('/auth/signin', {
        password: 'TestPassword123!'
      });

      expectErrorResponse(response, 400, 'Email and password are required');
    });

    it('should return error for missing password', async () => {
      const response = await authClient.post('/auth/signin', {
        email: '<EMAIL>'
      });

      expectErrorResponse(response, 400, 'Email and password are required');
    });

    it('should update last login timestamp', async () => {
      // First signin
      const firstResponse = await authClient.signin(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password
      );
      expectSuccessResponse(firstResponse, 200);
      const firstLoginTime = new Date();

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Second signin
      const secondResponse = await authClient.signin(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password
      );
      expectSuccessResponse(secondResponse, 200);

      // The second login should have a later timestamp
      // Note: We can't easily verify this without querying the database directly
      // This test mainly ensures the endpoint works multiple times
    });
  });

  describe('POST /auth/refresh', () => {
    let refreshToken: string;

    beforeEach(async () => {
      // Create user and get refresh token
      await authClient.signup(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password,
        'testuser'
      );

      const signinResponse = await authClient.signin(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password
      );

      refreshToken = signinResponse.body.tokens.refresh_token;
    });

    it('should refresh tokens successfully', async () => {
      const response = await authClient.refreshToken(refreshToken);

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);

      expect(response.body.message).toBe('Token refreshed successfully');
      expect(response.body.tokens).toBeDefined();
      expect(response.body.tokens.access_token).toBeDefined();
      expect(response.body.tokens.id_token).toBeDefined();
      // Refresh token is not returned in refresh response
      expect(response.body.tokens.refresh_token).toBeUndefined();
    });

    it('should return error for invalid refresh token', async () => {
      const response = await authClient.refreshToken('invalid-refresh-token');

      expectErrorResponse(response, 401, 'Invalid refresh token');
    });

    it('should return error for missing refresh token', async () => {
      const response = await authClient.post('/auth/refresh', {});

      expectErrorResponse(response, 400, 'Refresh token is required');
    });
  });

  describe('POST /auth/signout', () => {
    let accessToken: string;

    beforeEach(async () => {
      // Create user and get access token
      await authClient.signup(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password,
        'testuser'
      );

      const signinResponse = await authClient.signin(
        TEST_CREDENTIALS.VALID.email,
        TEST_CREDENTIALS.VALID.password
      );

      accessToken = signinResponse.body.tokens.access_token;
    });

    it('should sign out user successfully', async () => {
      const response = await authClient.signout(accessToken);

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);

      expect(response.body.message).toBe('Signed out successfully');
    });

    it('should return error for missing authorization header', async () => {
      const response = await authClient.post('/auth/signout', {});

      expectErrorResponse(response, 401, 'Authorization header required');
    });

    it('should return error for invalid authorization header format', async () => {
      const response = await authClient.post('/auth/signout', {}, {
        Authorization: 'InvalidFormat token'
      });

      expectErrorResponse(response, 401, 'Authorization header required');
    });

    it('should return error for invalid access token', async () => {
      const response = await authClient.signout('invalid-access-token');

      expect(response.statusCode).toBeGreaterThanOrEqual(400);
      // The exact error depends on Cognito's response
    });
  });

  describe('OPTIONS requests (CORS)', () => {
    it('should handle OPTIONS for /auth/signup', async () => {
      const response = await authClient.options('/auth/signup');

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);
    });

    it('should handle OPTIONS for /auth/signin', async () => {
      const response = await authClient.options('/auth/signin');

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);
    });

    it('should handle OPTIONS for /auth/refresh', async () => {
      const response = await authClient.options('/auth/refresh');

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);
    });

    it('should handle OPTIONS for /auth/signout', async () => {
      const response = await authClient.options('/auth/signout');

      expectSuccessResponse(response, 200);
      expectCorsHeaders(response);
    });
  });

  describe('Invalid endpoints', () => {
    it('should return 404 for unknown auth endpoints', async () => {
      const response = await authClient.get('/auth/unknown');

      expectErrorResponse(response, 404, 'Endpoint not found');
      expectCorsHeaders(response);
    });

    it('should return 404 for wrong HTTP method', async () => {
      const response = await authClient.get('/auth/signup');

      expectErrorResponse(response, 404, 'Endpoint not found');
    });
  });
});
