/**
 * Test data generators and mock data for GameFlex tests
 */

import { v4 as uuidv4 } from 'uuid';

export interface TestUser {
  id: string;
  cognito_user_id: string;
  email: string;
  username: string;
  display_name?: string;
  avatar_url?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface TestPost {
  id: string;
  user_id: string;
  title: string;
  content?: string;
  media_url?: string;
  media_type?: 'image' | 'video';
  is_public: boolean;
  created_at: string;
  updated_at: string;
  likes_count: number;
  comments_count: number;
}

export interface TestMedia {
  id: string;
  user_id: string;
  filename: string;
  original_filename: string;
  content_type: string;
  file_size: number;
  s3_key: string;
  s3_bucket: string;
  is_processed: boolean;
  created_at: string;
  updated_at: string;
}

export class TestDataGenerator {
  static createUser(overrides: Partial<TestUser> = {}): TestUser {
    const now = new Date().toISOString();
    const username = `testuser_${Math.random().toString(36).substring(7)}`;
    
    return {
      id: uuidv4(),
      cognito_user_id: `cognito_${uuidv4()}`,
      email: `${username}@test.gameflex.com`,
      username,
      display_name: username,
      avatar_url: null,
      is_active: true,
      is_verified: true,
      created_at: now,
      updated_at: now,
      last_login: now,
      ...overrides
    };
  }

  static createPost(userId?: string, overrides: Partial<TestPost> = {}): TestPost {
    const now = new Date().toISOString();
    
    return {
      id: uuidv4(),
      user_id: userId || uuidv4(),
      title: `Test Post ${Math.random().toString(36).substring(7)}`,
      content: 'This is a test post content',
      media_url: null,
      media_type: null,
      is_public: true,
      created_at: now,
      updated_at: now,
      likes_count: 0,
      comments_count: 0,
      ...overrides
    };
  }

  static createMedia(userId?: string, overrides: Partial<TestMedia> = {}): TestMedia {
    const now = new Date().toISOString();
    const filename = `test_image_${Math.random().toString(36).substring(7)}.jpg`;
    
    return {
      id: uuidv4(),
      user_id: userId || uuidv4(),
      filename,
      original_filename: filename,
      content_type: 'image/jpeg',
      file_size: 1024000,
      s3_key: `media/${filename}`,
      s3_bucket: 'gameflex-media-development',
      is_processed: true,
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  static createAuthRequest(overrides: any = {}) {
    return {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      username: 'testuser',
      ...overrides
    };
  }

  static createAPIGatewayEvent(overrides: any = {}) {
    return {
      httpMethod: 'GET',
      path: '/test',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      queryStringParameters: null,
      pathParameters: null,
      body: null,
      isBase64Encoded: false,
      requestContext: {
        requestId: uuidv4(),
        stage: 'development',
        resourcePath: '/test',
        httpMethod: 'GET',
        requestTime: new Date().toISOString(),
        protocol: 'HTTP/1.1',
        resourceId: 'test-resource',
        accountId: '************',
        apiId: 'test-api-id',
        identity: {
          sourceIp: '127.0.0.1',
          userAgent: 'test-agent'
        }
      },
      ...overrides
    };
  }

  static createLambdaContext() {
    return {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test-function',
      functionVersion: '$LATEST',
      invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
      memoryLimitInMB: '256',
      awsRequestId: uuidv4(),
      logGroupName: '/aws/lambda/test-function',
      logStreamName: '2024/01/01/[$LATEST]test',
      getRemainingTimeInMillis: () => 30000,
      done: () => {},
      fail: () => {},
      succeed: () => {}
    };
  }
}

// Pre-defined test users for consistent testing
export const TEST_USERS = {
  VALID_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'devuser',
    display_name: 'Dev User'
  }),
  
  ADMIN_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'admin',
    display_name: 'Admin User'
  }),
  
  INACTIVE_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'inactive',
    is_active: false
  }),
  
  UNVERIFIED_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'unverified',
    is_verified: false
  })
};

export const TEST_CREDENTIALS = {
  VALID: {
    email: '<EMAIL>',
    password: 'DevPassword123!'
  },
  
  INVALID_EMAIL: {
    email: '<EMAIL>',
    password: 'SomePassword123!'
  },
  
  INVALID_PASSWORD: {
    email: '<EMAIL>',
    password: 'WrongPassword123!'
  }
};
